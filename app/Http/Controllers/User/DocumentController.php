<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\Document\StoreDocumentRequest;
use App\Models\Document;
use App\Services\User\DocumentService;
use Illuminate\Support\Facades\Auth;
use Spatie\Image\Exceptions\InvalidImageDriver;

class DocumentController extends Controller
{
    public function __construct(
        protected DocumentService $service,
    ) {}

    public function index()
    {
        $documents = Document::where('user_id', Auth::user()->id)->with('file')->get();

        if ($documents->isEmpty()) {
            return [
                'message' => 'No documents found',
                'documents' => []
            ];
        }

        return [
            'documents' => $documents->map(function ($document) {
                return [
                    'id' => $document->id,
                    'name' => $document->name,
                    'type' => $document->type,
                    'status' => $document->status,
                    'file' => $document->file ? $document->file->url : null,
                    'created_at' => $document->created_at,
                ];
            })
        ];
    }
    
    /**
     * @throws InvalidImageDriver
     */
    public function store(StoreDocumentRequest $request)
    {
        return $this->service->storeDocument($request->validated());
    }
}
