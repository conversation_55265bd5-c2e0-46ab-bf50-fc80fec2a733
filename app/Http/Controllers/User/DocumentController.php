<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\Document\StoreDocumentRequest;
use App\Models\Document;
use App\Services\User\DocumentService;
use Illuminate\Support\Facades\Auth;
use Spatie\Image\Exceptions\InvalidImageDriver;

class DocumentController extends Controller
{
    public function __construct(
        protected DocumentService $service,
    ) {}

    public function index()
    {
        $documents = Document::where('user_id', Auth::user()->id)->with('file')->get();

        if ($documents->isEmpty()) {
            return [
                'message' => 'No documents found',
                'documents' => []
            ];
        }

        return [
            'documents' => $documents->map(function ($document) {
                return [
                    'id' => $document->id,
                    'name' => $document->name,
                    'type' => $document->type,
                    'status' => $document->status,
                    'file' => $document->file ? $document->file->url : null,
                    'created_at' => $document->created_at,
                ];
            })
        ];
    }
    
    /**
     * @throws InvalidImageDriver
     */
    public function store(StoreDocumentRequest $request)
    {
        return $this->service->storeDocument($request->validated());
    }

    /**
     * Display the specified document.
     */
    public function show($id)
    {
        $document = Document::where('user_id', Auth::user()->id)
            ->where('id', $id)
            ->with('file')
            ->first();

        if (!$document) {
            return response()->json([
                'message' => 'Document not found'
            ], 404);
        }

        return [
            'document' => [
                'id' => $document->id,
                'name' => $document->name,
                'type' => $document->type,
                'status' => $document->status,
                'description' => $document->description,
                'file' => $document->file?->url,
                'created_at' => $document->created_at,
                'updated_at' => $document->updated_at,
            ]
        ];
    }

    /**
     * Get user's document verification status.
     */
    public function verificationStatus()
    {
        $user = Auth::user();
        $requiredDocuments = ['id', 'id_back', 'selfie', 'consent'];

        // Get user's documents
        $userDocuments = Document::where('user_id', $user->id)
            ->whereIn('name', $requiredDocuments)
            ->get()
            ->keyBy('name');

        $documentStatus = [];
        $missingDocuments = [];
        $isFullyVerified = true;
        $hasAnyPending = false;
        $hasAnyRejected = false;

        foreach ($requiredDocuments as $docType) {
            if (isset($userDocuments[$docType])) {
                $document = $userDocuments[$docType];
                $documentStatus[$docType] = [
                    'id' => $document->id,
                    'status' => $document->status,
                    'description' => $document->description,
                    'created_at' => $document->created_at,
                    'updated_at' => $document->updated_at,
                ];

                if ($document->status !== 'approved') {
                    $isFullyVerified = false;
                }
                if ($document->status === 'pending') {
                    $hasAnyPending = true;
                }
                if ($document->status === 'rejected') {
                    $hasAnyRejected = true;
                }
            } else {
                $missingDocuments[] = $docType;
                $isFullyVerified = false;
            }
        }

        // Determine overall verification status
        $overallStatus = 'unverified';
        if ($isFullyVerified) {
            $overallStatus = 'verified';
        } elseif ($hasAnyPending) {
            $overallStatus = 'pending';
        } elseif ($hasAnyRejected) {
            $overallStatus = 'rejected';
        } elseif (!empty($documentStatus)) {
            $overallStatus = 'partial';
        }

        $completionPercentage = (count($documentStatus) / count($requiredDocuments)) * 100;

        return [
            'verification_status' => [
                'overall_status' => $overallStatus,
                'is_fully_verified' => $isFullyVerified,
                'completion_percentage' => round($completionPercentage, 2),
                'required_documents' => $requiredDocuments,
                'document_status' => $documentStatus,
                'missing_documents' => $missingDocuments,
                'has_pending' => $hasAnyPending,
                'has_rejected' => $hasAnyRejected,
            ]
        ];
    }
}
