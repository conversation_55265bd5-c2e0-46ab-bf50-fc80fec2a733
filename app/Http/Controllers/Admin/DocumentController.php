<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Document\UpdateDocumentRequest;
use App\Http\Resources\Admin\Document\IndexDocument;
use App\Models\Document;
use App\Models\User;
use App\Services\Admin\DocumentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DocumentController extends Controller
{
    public function __construct(
        protected DocumentService $service
    ) {}

    public function index(Request $request)
    {
        // Validate request parameters
        $validated = $request->validate([
            'per_page' => 'integer|nullable',
            'page' => 'integer|nullable',
            'user_id' => 'integer|exists:users,id|nullable',
            'status' => 'string|nullable',
            'type' => 'string|nullable',
            'search' => 'string|nullable',
        ]);

        // Get documents with filters
        $query = Document::with(['user', 'file']);

        // Apply filters
        if (isset($validated['user_id'])) {
            $query->where('user_id', $validated['user_id']);
        }

        if (isset($validated['status'])) {
            $query->where('status', $validated['status']);
        }

        if (isset($validated['type'])) {
            $query->where('name', $validated['type']);
        }

        // Apply search
        if (isset($validated['search'])) {
            $search = $validated['search'];
            $query->whereHas('user', function($q) use ($search) {
                $q->where('firstname', 'like', "%{$search}%")
                  ->orWhere('lastname', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Get paginated results
        $perPage = $validated['per_page'] ?? 15;
        $documents = $query->latest()->paginate($perPage);

        // Get statistics
        $totalDocuments = Document::count();
        $pendingDocuments = Document::where('status', 'pending')->count();
        $approvedDocuments = Document::where('status', 'approved')->count();
        $rejectedDocuments = Document::where('status', 'rejected')->count();

        // Return view with data
        return view('admin.documents.index', compact(
            'documents',
            'totalDocuments',
            'pendingDocuments',
            'approvedDocuments',
            'rejectedDocuments'
        ));
    }

    public function show($id): object|array
    {
        return $this->service->showDocument($id);
    }

    public function file(Document $document): StreamedResponse
    {
        return Storage::response($document->path);
    }

    public function grouped(Request $request)
    {
        // Validate request parameters
        $validated = $request->validate([
            'per_page' => 'integer|nullable',
            'page' => 'integer|nullable',
            'status' => 'string|nullable',
            'search' => 'string|nullable',
        ]);

        // Required document types
        $requiredDocuments = ['id', 'id_back', 'selfie', 'consent'];

        // Get users who have submitted at least one document
        $query = User::whereHas('documents')
            ->with(['documents' => function($query) {
                $query->with('file')->whereIn('name', ['id', 'id_back', 'selfie', 'consent']);
            }]);

        // Apply search filter
        if (isset($validated['search'])) {
            $search = $validated['search'];
            $query->where(function($q) use ($search) {
                $q->where('firstname', 'like', "%{$search}%")
                  ->orWhere('lastname', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('national_id', 'like', "%{$search}%");
            });
        }

        // Apply status filter (filter by document status)
        if (isset($validated['status'])) {
            $query->whereHas('documents', function($q) use ($validated) {
                $q->where('status', $validated['status']);
            });
        }

        // Get paginated results
        $perPage = $validated['per_page'] ?? 15;
        $users = $query->latest()->paginate($perPage);

        // Process each user to determine completion status
        $processedUsers = $users->getCollection()->map(function ($user) use ($requiredDocuments) {
            $userDocuments = $user->documents->keyBy('name');

            // Create a new object with user data and computed properties
            $userData = $user->toArray();
            $userData['document_completion'] = [];
            $userData['missing_documents'] = [];
            $userData['is_complete'] = true;

            foreach ($requiredDocuments as $docType) {
                if (isset($userDocuments[$docType])) {
                    $userData['document_completion'][$docType] = $userDocuments[$docType];
                } else {
                    $userData['missing_documents'][] = $docType;
                    $userData['is_complete'] = false;
                }
            }

            $userData['completion_percentage'] = (count($userData['document_completion']) / count($requiredDocuments)) * 100;

            // Convert back to object for easy access in view
            return (object) array_merge($userData, [
                'user' => $user, // Keep original user object for relationships
                'documents' => $user->documents
            ]);
        });

        // Update the paginator with processed data
        $users->setCollection($processedUsers);

        // Get statistics
        $totalUsers = User::whereHas('documents')->count();
        $completeUsers = User::whereHas('documents', function($query) {
            $query->where('name', 'id');
        })->whereHas('documents', function($query) {
            $query->where('name', 'id_back');
        })->whereHas('documents', function($query) {
            $query->where('name', 'selfie');
        })->whereHas('documents', function($query) {
            $query->where('name', 'consent');
        })->count();

        $incompleteUsers = $totalUsers - $completeUsers;

        return view('admin.documents.grouped', compact(
            'users',
            'totalUsers',
            'completeUsers',
            'incompleteUsers',
            'requiredDocuments'
        ));
    }

    public function update($id, UpdateDocumentRequest $request)
    {
        $document = $this->service->updateDocument($id, [
            'status' => $request->status,
            'description' => $request->description ?? null  // اگر description نبود null قرار بده
        ]);

        if ($request->wantsJson()) {
            return $document;
        }

        // Flash message based on status
        $statusMessage = match($request->status) {
            'approved' => 'مدرک با موفقیت تایید شد',
            'rejected' => 'مدرک با موفقیت رد شد',
            default => 'وضعیت مدرک با موفقیت بروزرسانی شد'
        };

        return redirect()->route('admin.document.index')->with('success', $statusMessage);
    }
}
