<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\Admin\DashboardService;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Alert;

class DashboardController extends Controller
{
    public function __construct(
        protected DashboardService $service
    ) {}

    public function index()
    {
        // کاربران آنلاین - فقط کاربرانی که در 15 دقیقه اخیر فعال بوده‌اند
        $onlineUsers = User::whereHas('sessions', function($query) {
                $query->where('last_activity', '>=', now()->subMinutes(15)->timestamp);
            })

            ->select('id', 'firstname', 'lastname', 'email')
            ->limit(5)
            ->get();

        // دریافت هشدارهای اخیر کاربران
        $userAlerts = Alert::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $systemStatus = [
            'server' => [
                'status' => true, // یا false
                'message' => 'فعال', // یا 'غیرفعال'
                'uptime' => 100,
            ],
            'database' => [
                'status' => true,
                'message' => 'فعال',
                'uptime' => 1,
            ],
            'memory' => [
                'status' => $this->getMemoryStatus(),
                'usage' => $this->getMemoryUsage(),
            ],
            'cpu' => [
                'status' => $this->getCpuStatus(),
                'usage' => $this->getCpuUsage(),
            ],
        ];

        return view('admin.dashboard.index', compact('systemStatus', 'onlineUsers', 'userAlerts'));
    }

    private function getMemoryStatus()
    {
        $usage = $this->getMemoryUsage();
        if ($usage > 90) return 'danger';
        if ($usage > 70) return 'warning';
        return 'success';
    }

    private function getMemoryUsage()
    {
        // اینجا کد واقعی برای دریافت مصرف حافظه
        return memory_get_usage(true) / 1024 / 1024;
    }

    private function getCpuStatus()
    {
        $usage = $this->getCpuUsage();
        if ($usage > 90) return 'danger';
        if ($usage > 70) return 'warning';
        return 'info';
    }

    private function getCpuUsage()
    {
        // اینجا کد واقعی برای دریافت مصرف CPU
        return sys_getloadavg()[0] * 100;
    }
}
