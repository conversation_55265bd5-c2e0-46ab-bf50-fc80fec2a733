<?php

namespace App\Http\Controllers\Admin\User;

use App\Http\Controllers\Controller;
use App\Http\Repositories\AuthRepositories;
use App\Models\Activity;
use App\Models\Coin;
use App\Models\Network;
use App\Models\Role;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Http\Request;

class UserController extends Controller
{
    protected AuthRepositories $repository;

    public function __construct()
    {
        $this->repository = new AuthRepositories;
    }
    /**
     * Display a listing of the resource.
     */
    public function CreateWalletbyCoin(Request $request, $id)
    {
        $this->repository->createUserWalletByCoin($id, $request->coin_type);
        return redirect()->back();
    }
    public function index()
    {
        $users = User::with(['roles', 'documents', 'wallets', 'transactions'])->paginate(15);
        $activities = Activity::with('user')
            ->orderByDesc('created_at')
            ->paginate(20);

        // Add these statistics
        $totalUsers = User::count();
        $activeUsers = User::where('status', 'approved')->count();
        $newUsers = User::whereMonth('created_at', now()->month)->count();
        $onlineUsers = User::whereHas('sessions', function($query) {
            $query->where('last_activity', '>=', now()->subMinutes(15)->timestamp);
        })->count();

        // Get all roles for the search dropdown
        $roles = Role::all();

        // Get user registration data for charts
        $monthlyRegistrations = $this->getMonthlyRegistrations();
        $weeklyRegistrations = $this->getWeeklyRegistrations();
        $dailyRegistrations = $this->getDailyRegistrations();

        // Get verification status data for charts
        $verificationStats = $this->getVerificationStats();

        return view('admin.users.index', compact(
            'users',
            'activities',
            'totalUsers',
            'activeUsers',
            'newUsers',
            'onlineUsers',
            'roles',
            'monthlyRegistrations',
            'weeklyRegistrations',
            'dailyRegistrations',
            'verificationStats'
        ));
    }

    public function create()
    {
        $roles = Role::all();
        return view('admin.users.create', compact('roles'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'phone' => 'required|string|unique:users',
            'roles' => 'required|array',
        ]);

        $user = User::create($validated);
        $user->roles()->sync($request->roles);

        return redirect()->route('admin.users.index')
            ->with('success', 'کاربر با موفقیت ایجاد شد.');
    }

    public function edit($id)
    {
        $user = User::findOrFail($id);
        $roles = Role::all();
        return view('admin.users.edit', compact('user', 'roles'));
    }

    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $validated = $request->validate([
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,'.$id,
            'phone' => 'required|string|unique:users,phone,'.$id,
            'roles' => 'required|array',
        ]);

        $user->update($validated);
        $user->roles()->sync($request->roles);

        return redirect()->route('admin.users.index')
            ->with('success', 'کاربر با موفقیت بروزرسانی شد.');
    }

    public function destroy($id)
    {
        $user = User::findOrFail($id);
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'کاربر با موفقیت حذف شد.');
    }

    /**
     * Get monthly user registrations for the last 12 months
     */
    private function getMonthlyRegistrations()
    {
        $data = [];
        $labels = [];

        // Get data for the last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $count = User::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();

            $data[] = $count;
            $labels[] = $month->format('F');
        }

        return [
            'data' => $data,
            'labels' => $labels
        ];
    }

    /**
     * Get weekly user registrations for the last 7 days
     */
    private function getWeeklyRegistrations()
    {
        $data = [];
        $labels = [
            'شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنجشنبه', 'جمعه'
        ];

        // Get start of the week (Saturday in Iranian calendar)
        $startOfWeek = now()->startOfWeek(6); // 6 is Saturday in Carbon

        // Get data for each day of the week
        for ($i = 0; $i < 7; $i++) {
            $day = $startOfWeek->copy()->addDays($i);
            $count = User::whereDate('created_at', $day->toDateString())->count();
            $data[] = $count;
        }

        return [
            'data' => $data,
            'labels' => $labels
        ];
    }

    /**
     * Get hourly user registrations for the last 24 hours
     */
    private function getDailyRegistrations()
    {
        $data = [];
        $labels = [];

        // Get data for the last 24 hours
        for ($i = 0; $i < 24; $i++) {
            $hour = sprintf('%02d', $i);
            $labels[] = $hour;

            $count = User::whereDate('created_at', today())
                ->whereRaw('HOUR(created_at) = ?', [$i])
                ->count();

            $data[] = $count;
        }

        return [
            'data' => $data,
            'labels' => $labels
        ];
    }

    /**
     * Get verification status statistics
     */
    private function getVerificationStats()
    {
        // Count users with all required documents approved
        $fullyVerified = User::whereHas('documents', function($query) {
            $query->where('name', 'id')->where('status', 'approved');
        })->whereHas('documents', function($query) {
            $query->where('name', 'selfie')->where('status', 'approved');
        })->whereHas('documents', function($query) {
            $query->where('name', 'consent')->where('status', 'approved');
        })->count();

        // Count users with pending documents
        $pendingVerification = User::whereHas('documents', function($query) {
            $query->where('status', 'pending');
        })->count();

        // Count users with no documents or rejected documents
        $notVerified = $this->getTotalUsers() - $fullyVerified - $pendingVerification;

        return [
            'notVerified' => $notVerified,
            'fullyVerified' => $fullyVerified,
            'pendingVerification' => $pendingVerification
        ];
    }

    /**
     * Get total number of users
     */
    private function getTotalUsers()
    {
        return User::count();
    }

    public function show($id)
    {
        $user = User::findOrFail($id);

        // Get all active coins first
        $coins = Coin::where('status', 1)
            ->where(function($query) {
                $query->where('is_withdrawal', 1)
                      ->orWhere('is_deposit', 1);
            })
            ->get();

        // Get user wallets with coin information
        $wallets = Wallet::where('user_id', $id)
            ->join('coins', 'wallets.coin_id', '=', 'coins.id')
            ->select(
                'wallets.*',
                'coins.coin_type',
                'coins.coin_icon',
                'coins.is_withdrawal',
                'coins.is_deposit'
            )
            ->get();

        $networks = Network::where('status', 1)->get();

        // Calculate total balance in USD
        $totalBalanceUSD = 0;
        foreach($wallets as $wallet) {
            $balance = $wallet->balance ?? 0;
            $coinType = $wallet->coin_type;
            $totalBalanceUSD += get_coin_usd_value($balance, $coinType);
        }

        // Load other relationships
        $user->load([
            'cards.bank',
            'transactions.currency',
            'documents',
            'tickets.unit',
            'tickets.level',
            'roles'
        ]);

        return view('admin.users.show', compact(
            'user',
            'wallets',
            'coins',
            'networks',
            'totalBalanceUSD'
        ));
    }
}
