<?php

namespace App\Repositories\Admin;

use App\Models\Document;
use App\Repositories\RepositoriesContract;
use App\Repositories\Repository;

class DocumentRepo extends Repository implements RepositoriesContract
{
    public function getAllRecords($request)
    {
        $docs = Document::query();
        if (isset($request['user_id'])) {
            $docs->where('user_id', $request['user_id']);
        }
        if (isset($request['status'])) {
            $docs->whereIn('status', explode(',', $request['status']));
        }

        return $docs->with('file', 'user')->paginate(
            perPage: $request['per_page'] ?? 30,
            page: $request['page'] ?? 1
        );

    }

    public function createRecord(array $data) {}

    public function getRecordById($id)
    {
        return Document::with('file')->findOrFail($id);
    }

    public function updateRecord($id, array $data)
    {
        $document = Document::findOrFail($id);
        $document->status = $data['status'];
        $document->description = $data['description'];
        $document->save();
    }

    public function deleteRecordById($id) {}
}
